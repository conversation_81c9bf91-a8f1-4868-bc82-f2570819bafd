# Python构建产物
*.egg-info/
build/
dist/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# 虚拟环境
.venv/
venv/
.env

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# macOS文件
.DS_Store
.AppleDouble
.LSOverride

# 日志文件
*.log
logs/*.log

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
temp/
tmp/
*.tmp

# Node.js (将来Tauri前端会需要)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Tauri构建产物
src-tauri/target/
src-tauri/Cargo.lock
src-tauri/bin/

# 生产环境配置 (避免意外提交真实密码)
config/user.json
config/production.local.json