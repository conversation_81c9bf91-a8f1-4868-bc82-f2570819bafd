# 首页策略任务版块重新设计 - 设计规格书

## 设计概述

### 设计目标
基于"弹出框风格"设计系统，重新设计策略任务版块，实现信息清楚、操作便利、视觉美观的用户界面。重点解决现有界面信息过密、层次不清、交互复杂的问题。

### 设计原则
1. **信息分层**: 重要信息优先显示，次要信息适当隐藏
2. **状态驱动**: 以任务状态和盈亏为核心的视觉设计
3. **操作简化**: 减少认知负担，突出关键操作
4. **响应适配**: 确保在不同屏幕尺寸下的良好体验

## 系统架构设计

### 组件架构图

```mermaid
graph TD
    A[Dashboard 主页面] --> B[GlobalControls 全局控制区]
    A --> C[TaskList 任务列表]
    
    B --> B1[GlobalStats 全局统计]
    B --> B2[QuickActions 快速操作]
    B --> B3[ConnectionStatus 连接状态]
    
    C --> C1[TaskFilter 任务筛选器]
    C --> C2[TaskGrid 任务网格]
    C --> C3[EmptyState 空状态]
    
    C2 --> D[TaskCard 任务卡片]
    
    D --> D1[TaskHeader 卡片头部]
    D --> D2[TaskStatus 状态指示器]
    D --> D3[TaskMetrics 关键指标]
    D --> D4[TaskActions 操作按钮]
    
    D1 --> D1A[StockInfo 股票信息]
    D1 --> D1B[StrategyInfo 策略信息]
    
    D3 --> D3A[PnLDisplay 盈亏显示]
    D3 --> D3B[PositionInfo 持仓信息]
    D3 --> D3C[PerformanceIndicator 性能指标]
    
    D4 --> D4A[PrimaryActions 主要操作]
    D4 --> D4B[SecondaryActions 次要操作]
```

### 数据流设计

```mermaid
flowchart LR
    A[useDashboard Hook] --> B[TaskList Component]
    B --> C[TaskCard Component]
    
    A --> D[Task Data]
    A --> E[Global Status]
    A --> F[Filter State]
    
    D --> G[Task Status]
    D --> H[Position Data]
    D --> I[PnL Data]
    
    C --> J[Status Display]
    C --> K[Metrics Display]
    C --> L[Action Handlers]
    
    L --> M[onToggleTask]
    L --> N[onShowDetails]
    L --> O[onLiquidateTask]
    
    M --> A
    N --> A
    O --> A
```

## 详细组件设计

### 1. TaskCard 任务卡片重新设计

#### 设计理念
- **视觉分层**: 使用卡片嵌套和颜色对比创建清晰层次
- **状态驱动**: 通过颜色和图标突出任务状态
- **信息精简**: 只显示关键决策信息，详细信息通过交互获取

#### 组件结构

```typescript
interface TaskCardProps {
    task: Task;
    variant?: 'compact' | 'standard' | 'detailed';
    onToggle: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEdit: (taskId: string) => void;
    onDelete: (taskId: string) => void;
    onLiquidate: (taskId: string) => void;
}

interface TaskCardLayout {
    header: TaskCardHeaderSection;
    statusIndicator: TaskStatusSection;
    metricsPanel: TaskMetricsSection;
    actionsFooter: TaskActionsSection;
}
```

#### 视觉层次设计

```mermaid
graph TD
    A[TaskCard Container] --> B[Header Section]
    A --> C[Status Indicator]
    A --> D[Metrics Panel]
    A --> E[Actions Footer]
    
    B --> B1[Stock Name + Code]
    B --> B2[Strategy Name]
    
    C --> C1[Status Badge]
    C --> C2[Running Duration]
    
    D --> D1[PnL Display - Primary]
    D --> D2[Position Info - Secondary]
    D --> D3[Performance Chart - Tertiary]
    
    E --> E1[Primary Actions]
    E --> E2[Secondary Actions Menu]
```

#### 样式规范

```css
/* 主卡片容器 - 遵循"弹出框风格" */
.task-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    position: relative;
    transition: all 0.2s ease;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 状态边框增强 */
.task-card.status-running {
    border-left: 4px solid #28a745;
}

.task-card.status-error {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

/* 内容卡片区域 */
.task-card-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;
}
```

#### 分层信息展示

**第一层级 - 核心识别信息**
- 股票名称和代码（大字体，加粗）
- 策略名称（中等字体，副标题色）
- 状态指示器（彩色徽章 + 动画）

**第二层级 - 关键决策信息**
- 浮动盈亏（特大字体，红绿配色）
- 盈亏百分比（大字体，红绿配色）
- 持仓数量（中等字体）

**第三层级 - 辅助信息**
- 成本价格（小字体）
- 当前价格（小字体）
- 运行时长（小字体）

### 2. TaskStatus 状态指示器重新设计

#### 状态视觉映射

```typescript
const statusConfig = {
    running: {
        color: '#28a745',
        bgColor: '#d4edda',
        icon: '▶️',
        pulse: true,
        borderColor: '#28a745'
    },
    paused: {
        color: '#ffc107',
        bgColor: '#fff3cd',
        icon: '⏸️',
        pulse: false,
        borderColor: '#ffc107'
    },
    error: {
        color: '#dc3545',
        bgColor: '#f8d7da',
        icon: '❌',
        pulse: true,
        borderColor: '#dc3545'
    },
    stopped: {
        color: '#6c757d',
        bgColor: '#e9ecef',
        icon: '⏹️',
        pulse: false,
        borderColor: '#6c757d'
    },
    liquidated: {
        color: '#007bff',
        bgColor: '#d1ecf1',
        icon: '💧',
        pulse: false,
        borderColor: '#007bff'
    }
};
```

#### 动态状态效果

```css
/* 运行状态脉动效果 */
.status-indicator.running {
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
}

/* 错误状态闪烁效果 */
.status-indicator.error {
    animation: errorBlink 1s infinite alternate;
}

@keyframes errorBlink {
    0% { background-color: #f8d7da; }
    100% { background-color: #f5c6cb; }
}
```

### 3. TaskMetrics 关键指标显示

#### 盈亏显示重新设计

```typescript
interface PnLDisplayProps {
    pnl: number;
    pnlPercentage: number;
    trend?: 'up' | 'down' | 'stable';
    showTrend?: boolean;
}

const PnLDisplay: React.FC<PnLDisplayProps> = ({ pnl, pnlPercentage, trend, showTrend }) => {
    const isProfit = pnl > 0;
    const isLoss = pnl < 0;
    
    return (
        <div className={`pnl-display ${isProfit ? 'profit' : isLoss ? 'loss' : 'neutral'}`}>
            <div className="pnl-amount">
                {isProfit && <span className="trend-icon">▲</span>}
                {isLoss && <span className="trend-icon">▼</span>}
                {formatCurrency(pnl)}
            </div>
            <div className="pnl-percentage">
                ({pnlPercentage > 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%)
            </div>
            {showTrend && trend && <TrendIndicator trend={trend} />}
        </div>
    );
};
```

#### 样式规范

```css
.pnl-display {
    padding: 12px;
    border-radius: 6px;
    text-align: center;
    margin: 8px 0;
}

.pnl-display.profit {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #28a745;
    color: #155724;
}

.pnl-display.loss {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #dc3545;
    color: #721c24;
}

.pnl-amount {
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.pnl-percentage {
    font-size: 14px;
    opacity: 0.8;
}

.trend-icon {
    font-size: 12px;
    animation: trendPulse 1.5s infinite;
}
```

### 4. TaskActions 操作按钮重新设计

#### 操作分级

**主要操作（始终可见）**
- 启动/暂停切换按钮
- 详情查看按钮

**次要操作（下拉菜单）**
- 编辑任务
- 清仓操作
- 删除任务

#### 组件设计

```typescript
interface TaskActionsProps {
    task: Task;
    onToggle: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEdit: (taskId: string) => void;
    onLiquidate: (taskId: string) => void;
    onDelete: (taskId: string) => void;
}

const TaskActions: React.FC<TaskActionsProps> = ({ task, ...handlers }) => {
    const [showSecondaryMenu, setShowSecondaryMenu] = useState(false);
    
    return (
        <div className="task-actions">
            <div className="primary-actions">
                <ToggleButton task={task} onToggle={handlers.onToggle} />
                <DetailsButton taskId={task.id} onShowDetails={handlers.onShowDetails} />
            </div>
            
            <div className="secondary-actions">
                <DropdownMenu
                    isOpen={showSecondaryMenu}
                    onToggle={setShowSecondaryMenu}
                    items={[
                        { label: '编辑', icon: '✏️', action: () => handlers.onEdit(task.id) },
                        { label: '清仓', icon: '🚨', action: () => handlers.onLiquidate(task.id), 
                          disabled: task.status !== 'running' || task.position === 0 },
                        { label: '删除', icon: '🗑️', action: () => handlers.onDelete(task.id), 
                          disabled: task.status === 'running', style: 'danger' }
                    ]}
                />
            </div>
        </div>
    );
};
```

### 5. TaskFilter 筛选器重新设计

#### 筛选器视觉设计

```typescript
interface FilterButtonProps {
    status: TaskStatus | 'all';
    count: number;
    isActive: boolean;
    onClick: () => void;
}

const FilterButton: React.FC<FilterButtonProps> = ({ status, count, isActive, onClick }) => {
    const config = filterConfig[status];
    
    return (
        <button 
            className={`filter-button ${isActive ? 'active' : ''} ${status}`}
            onClick={onClick}
        >
            <span className="filter-icon">{config.icon}</span>
            <span className="filter-label">{config.label}</span>
            <span className="filter-count">{count}</span>
        </button>
    );
};
```

#### 筛选器配置

```typescript
const filterConfig = {
    all: { icon: '📊', label: '全部', color: '#6c757d' },
    running: { icon: '▶️', label: '运行中', color: '#28a745' },
    paused: { icon: '⏸️', label: '暂停', color: '#ffc107' },
    stopped: { icon: '⏹️', label: '停止', color: '#6c757d' },
    error: { icon: '❌', label: '错误', color: '#dc3545' },
    liquidated: { icon: '💧', label: '已清仓', color: '#007bff' }
};
```

### 6. 响应式布局设计

#### 断点定义

```typescript
const breakpoints = {
    xs: '0px',      // 手机竖屏
    sm: '576px',    // 手机横屏
    md: '768px',    // 平板
    lg: '992px',    // 笔记本
    xl: '1200px',   // 桌面
    xxl: '1400px'   // 大屏桌面
};
```

#### 网格布局配置

```typescript
const gridConfig = {
    xs: { columns: 1, gap: '12px', cardPadding: '12px' },
    sm: { columns: 1, gap: '16px', cardPadding: '16px' },
    md: { columns: 2, gap: '16px', cardPadding: '16px' },
    lg: { columns: 2, gap: '20px', cardPadding: '16px' },
    xl: { columns: 3, gap: '20px', cardPadding: '16px' },
    xxl: { columns: 4, gap: '24px', cardPadding: '20px' }
};
```

#### 响应式样式

```css
/* 基础网格 */
.task-grid {
    display: grid;
    gap: var(--grid-gap);
    padding: var(--container-padding);
}

/* 响应式网格列数 */
@media (min-width: 0px) {
    .task-grid { 
        grid-template-columns: repeat(1, 1fr);
        --grid-gap: 12px;
        --container-padding: 12px;
    }
}

@media (min-width: 768px) {
    .task-grid { 
        grid-template-columns: repeat(2, 1fr);
        --grid-gap: 16px;
        --container-padding: 16px;
    }
}

@media (min-width: 1200px) {
    .task-grid { 
        grid-template-columns: repeat(3, 1fr);
        --grid-gap: 20px;
        --container-padding: 20px;
    }
}

@media (min-width: 1400px) {
    .task-grid { 
        grid-template-columns: repeat(4, 1fr);
        --grid-gap: 24px;
        --container-padding: 24px;
    }
}
```

## 交互设计规范

### 1. 动画效果设计

#### 卡片动画

```css
/* 卡片进入动画 */
@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-card {
    animation: cardSlideIn 0.3s ease-out;
}

/* 卡片hover效果 */
.task-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
}
```

#### 状态变化动画

```css
/* 状态变化过渡 */
.status-indicator {
    transition: all 0.3s ease;
}

.pnl-display {
    transition: all 0.2s ease;
}

/* 数值变化高亮效果 */
@keyframes valueHighlight {
    0% { background-color: rgba(255, 235, 59, 0.3); }
    100% { background-color: transparent; }
}

.value-changed {
    animation: valueHighlight 1s ease;
}
```

### 2. 加载状态设计

#### 卡片骨架屏

```typescript
const TaskCardSkeleton: React.FC = () => (
    <div className="task-card skeleton">
        <div className="skeleton-header">
            <div className="skeleton-line skeleton-title"></div>
            <div className="skeleton-line skeleton-subtitle"></div>
        </div>
        <div className="skeleton-metrics">
            <div className="skeleton-box skeleton-pnl"></div>
            <div className="skeleton-line skeleton-position"></div>
        </div>
        <div className="skeleton-actions">
            <div className="skeleton-button"></div>
            <div className="skeleton-button"></div>
        </div>
    </div>
);
```

#### 骨架屏样式

```css
.skeleton {
    pointer-events: none;
}

.skeleton-line, .skeleton-box, .skeleton-button {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

.skeleton-title {
    height: 20px;
    width: 60%;
    margin-bottom: 8px;
}

.skeleton-subtitle {
    height: 16px;
    width: 40%;
    margin-bottom: 12px;
}

.skeleton-pnl {
    height: 40px;
    width: 80%;
    margin: 12px auto;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
```

### 3. 错误状态设计

#### 错误边界组件

```typescript
interface TaskCardErrorBoundaryProps {
    children: React.ReactNode;
    taskId: string;
}

const TaskCardErrorBoundary: React.FC<TaskCardErrorBoundaryProps> = ({ children, taskId }) => {
    const [hasError, setHasError] = useState(false);
    
    if (hasError) {
        return (
            <div className="task-card error-card">
                <div className="error-content">
                    <div className="error-icon">⚠️</div>
                    <div className="error-message">
                        任务卡片加载失败
                    </div>
                    <button 
                        className="error-retry"
                        onClick={() => setHasError(false)}
                    >
                        重试
                    </button>
                </div>
            </div>
        );
    }
    
    return <>{children}</>;
};
```

## 性能优化设计

### 1. 组件优化策略

#### 记忆化组件

```typescript
// 使用React.memo优化TaskCard组件
export const TaskCard = React.memo<TaskCardProps>(({ task, ...handlers }) => {
    // 组件实现
}, (prevProps, nextProps) => {
    // 自定义比较逻辑
    return (
        prevProps.task.id === nextProps.task.id &&
        prevProps.task.status === nextProps.task.status &&
        prevProps.task.pnl === nextProps.task.pnl &&
        prevProps.task.position === nextProps.task.position
    );
});

// 使用useMemo优化计算属性
const TaskMetrics: React.FC<TaskMetricsProps> = ({ task }) => {
    const pnlPercentage = useMemo(() => {
        if (!task.avgCost || task.position === 0) return 0;
        return (task.pnl / (task.avgCost * task.position)) * 100;
    }, [task.pnl, task.avgCost, task.position]);
    
    const currentPrice = useMemo(() => {
        if (!task.avgCost || task.position === 0) return null;
        return task.avgCost + (task.pnl / task.position);
    }, [task.avgCost, task.pnl, task.position]);
    
    return (
        <div className="task-metrics">
            <PnLDisplay pnl={task.pnl} percentage={pnlPercentage} />
            <PositionInfo position={task.position} avgCost={task.avgCost} currentPrice={currentPrice} />
        </div>
    );
};
```

#### 虚拟化列表

```typescript
// 对于大量任务的情况，使用虚拟化
import { FixedSizeGrid as Grid } from 'react-window';

const VirtualizedTaskGrid: React.FC<VirtualizedTaskGridProps> = ({ 
    tasks, 
    columns, 
    cardHeight = 320,
    cardWidth = 300 
}) => {
    const Row = ({ columnIndex, rowIndex, style }) => {
        const taskIndex = rowIndex * columns + columnIndex;
        const task = tasks[taskIndex];
        
        if (!task) return null;
        
        return (
            <div style={style}>
                <TaskCard task={task} {...handlers} />
            </div>
        );
    };
    
    return (
        <Grid
            columnCount={columns}
            rowCount={Math.ceil(tasks.length / columns)}
            columnWidth={cardWidth}
            rowHeight={cardHeight}
            width={columns * cardWidth}
            height={600}
        >
            {Row}
        </Grid>
    );
};
```

### 2. 状态管理优化

#### 分层状态管理

```typescript
// 全局状态（通过Context）
interface DashboardContextState {
    globalStatus: GlobalStatus;
    connectionStatus: ConnectionStatus;
    userPreferences: UserPreferences;
}

// 本地状态（组件内部）
interface TaskListLocalState {
    filterStatus: FilterStatus;
    sortOrder: SortOrder;
    isLoading: boolean;
    error: string | null;
}

// 任务特定状态（单个TaskCard内部）
interface TaskCardLocalState {
    isExpanded: boolean;
    isLoading: boolean;
    showConfirmDialog: boolean;
}
```

#### 状态更新优化

```typescript
// 使用useCallback优化事件处理器
const TaskList: React.FC<TaskListProps> = ({ tasks, onUpdateTask }) => {
    const handleToggleTask = useCallback((taskId: string) => {
        onUpdateTask(taskId, { status: tasks.find(t => t.id === taskId)?.status === 'running' ? 'paused' : 'running' });
    }, [tasks, onUpdateTask]);
    
    const handleLiquidateTask = useCallback((taskId: string) => {
        onUpdateTask(taskId, { status: 'liquidated', position: 0 });
    }, [onUpdateTask]);
    
    // 使用稳定的引用避免不必要的重渲染
    const stableHandlers = useMemo(() => ({
        onToggle: handleToggleTask,
        onLiquidate: handleLiquidateTask
    }), [handleToggleTask, handleLiquidateTask]);
    
    return (
        <div className="task-list">
            {tasks.map(task => (
                <TaskCard 
                    key={task.id} 
                    task={task} 
                    {...stableHandlers}
                />
            ))}
        </div>
    );
};
```

## 可访问性设计

### 1. 键盘导航支持

```typescript
const TaskCard: React.FC<TaskCardProps> = ({ task, ...handlers }) => {
    const cardRef = useRef<HTMLDivElement>(null);
    
    const handleKeyDown = (event: React.KeyboardEvent) => {
        switch (event.key) {
            case 'Enter':
            case ' ':
                event.preventDefault();
                handlers.onShowDetails(task.id);
                break;
            case 'p':
                event.preventDefault();
                handlers.onToggle(task.id);
                break;
            case 'l':
                if (event.ctrlKey && task.position > 0) {
                    event.preventDefault();
                    handlers.onLiquidate(task.id);
                }
                break;
        }
    };
    
    return (
        <div
            ref={cardRef}
            className="task-card"
            tabIndex={0}
            role="button"
            aria-label={`${task.stockName} ${task.strategyName} 任务卡片`}
            onKeyDown={handleKeyDown}
        >
            {/* 卡片内容 */}
        </div>
    );
};
```

### 2. 屏幕阅读器支持

```typescript
const TaskStatus: React.FC<TaskStatusProps> = ({ status }) => {
    const statusText = {
        running: '运行中',
        paused: '已暂停',
        stopped: '已停止',
        error: '错误状态',
        liquidated: '已清仓'
    };
    
    return (
        <div 
            className={`status-indicator ${status}`}
            role="status"
            aria-label={`任务状态：${statusText[status]}`}
        >
            <span aria-hidden="true">{getStatusIcon(status)}</span>
            <span className="status-text">{statusText[status]}</span>
        </div>
    );
};

const PnLDisplay: React.FC<PnLDisplayProps> = ({ pnl, percentage }) => {
    const isProfit = pnl > 0;
    const statusText = isProfit ? '盈利' : pnl < 0 ? '亏损' : '持平';
    
    return (
        <div 
            className={`pnl-display ${isProfit ? 'profit' : 'loss'}`}
            role="text"
            aria-label={`浮动盈亏：${statusText} ${Math.abs(pnl)} 港币，收益率 ${percentage.toFixed(2)} 百分比`}
        >
            <div className="pnl-amount" aria-hidden="true">
                {formatCurrency(pnl)}
            </div>
            <div className="pnl-percentage" aria-hidden="true">
                ({percentage > 0 ? '+' : ''}{percentage.toFixed(2)}%)
            </div>
        </div>
    );
};
```

## 测试设计

### 1. 单元测试规范

```typescript
// TaskCard组件测试
describe('TaskCard Component', () => {
    const mockTask: Task = {
        id: 'test-task-1',
        name: '测试任务',
        stockCode: 'HK.00700',
        stockName: '腾讯控股',
        strategyName: '策略A: 大单监控',
        status: 'running',
        position: 1000,
        avgCost: 450.5,
        pnl: 5500,
        createdAt: new Date(),
        updatedAt: new Date(),
        strategyConfig: {} as StrategyConfig,
        riskConfig: {} as RiskConfig
    };
    
    it('应该正确显示任务基本信息', () => {
        render(<TaskCard task={mockTask} onToggle={jest.fn()} onShowDetails={jest.fn()} />);
        
        expect(screen.getByText('腾讯控股 (HK.00700)')).toBeInTheDocument();
        expect(screen.getByText('策略A: 大单监控')).toBeInTheDocument();
    });
    
    it('应该正确显示盈亏信息', () => {
        render(<TaskCard task={mockTask} onToggle={jest.fn()} onShowDetails={jest.fn()} />);
        
        expect(screen.getByText(/HK\$5,500\.00/)).toBeInTheDocument();
        expect(screen.getByText(/\+1\.22%/)).toBeInTheDocument();
    });
    
    it('应该根据状态显示正确的操作按钮', () => {
        const onToggle = jest.fn();
        render(<TaskCard task={mockTask} onToggle={onToggle} onShowDetails={jest.fn()} />);
        
        const toggleButton = screen.getByText('暂停');
        fireEvent.click(toggleButton);
        
        expect(onToggle).toHaveBeenCalledWith('test-task-1');
    });
});
```

### 2. 集成测试规范

```typescript
// TaskList组件集成测试
describe('TaskList Integration', () => {
    it('应该正确处理任务筛选', async () => {
        const mockTasks = [
            { ...mockTask, id: '1', status: 'running' as TaskStatus },
            { ...mockTask, id: '2', status: 'paused' as TaskStatus },
            { ...mockTask, id: '3', status: 'error' as TaskStatus }
        ];
        
        render(<TaskList tasks={mockTasks} onToggleTask={jest.fn()} onShowDetails={jest.fn()} />);
        
        // 默认显示所有任务
        expect(screen.getAllByTestId('task-card')).toHaveLength(3);
        
        // 点击"运行中"筛选器
        fireEvent.click(screen.getByText('运行中'));
        
        // 应该只显示运行中的任务
        await waitFor(() => {
            expect(screen.getAllByTestId('task-card')).toHaveLength(1);
        });
    });
});
```

### 3. 视觉回归测试

```typescript
// 使用Playwright进行视觉回归测试
import { test, expect } from '@playwright/test';

test('TaskCard视觉一致性测试', async ({ page }) => {
    await page.goto('/dashboard');
    
    // 等待任务卡片加载
    await page.waitForSelector('.task-card');
    
    // 截图对比
    await expect(page.locator('.task-list')).toHaveScreenshot('task-list-default.png');
    
    // 测试hover状态
    await page.locator('.task-card').first().hover();
    await expect(page.locator('.task-card').first()).toHaveScreenshot('task-card-hover.png');
    
    // 测试不同状态的卡片
    await page.selectOption('[data-testid="status-filter"]', 'error');
    await expect(page.locator('.task-list')).toHaveScreenshot('task-list-error-filtered.png');
});
```

## 兼容性考虑

### 1. 浏览器兼容性

```css
/* CSS变量回退 */
.task-card {
    background: #f8f9fa; /* 回退值 */
    background: var(--bg-color, #f8f9fa);
}

/* Grid布局回退 */
.task-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

@supports (display: grid) {
    .task-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* 现代特性检测 */
@supports (backdrop-filter: blur(10px)) {
    .task-card {
        backdrop-filter: blur(10px);
        background: rgba(248, 249, 250, 0.9);
    }
}
```

### 2. 性能降级策略

```typescript
// 检测设备性能并降级
const usePerformanceMode = () => {
    const [isLowPerformance, setIsLowPerformance] = useState(false);
    
    useEffect(() => {
        // 检测设备性能
        const connection = (navigator as any).connection;
        const deviceMemory = (navigator as any).deviceMemory;
        
        const isLowEnd = (
            connection?.effectiveType === '2g' ||
            connection?.effectiveType === 'slow-2g' ||
            deviceMemory < 4
        );
        
        setIsLowPerformance(isLowEnd);
    }, []);
    
    return {
        isLowPerformance,
        shouldDisableAnimations: isLowPerformance,
        shouldUseVirtualization: isLowPerformance
    };
};

const TaskList: React.FC<TaskListProps> = ({ tasks, ...props }) => {
    const { shouldDisableAnimations, shouldUseVirtualization } = usePerformanceMode();
    
    if (shouldUseVirtualization && tasks.length > 20) {
        return <VirtualizedTaskGrid tasks={tasks} {...props} />;
    }
    
    return (
        <div className={`task-list ${shouldDisableAnimations ? 'no-animations' : ''}`}>
            {tasks.map(task => (
                <TaskCard key={task.id} task={task} {...props} />
            ))}
        </div>
    );
};
```

## 实施计划

### 第一阶段：核心组件重构
1. **TaskCard组件重新设计** (3天)
   - 实现新的视觉层次
   - 优化盈亏显示
   - 简化操作按钮

2. **TaskStatus状态指示器** (1天)
   - 实现新的状态视觉映射
   - 添加动画效果

3. **响应式布局系统** (2天)
   - 实现多断点适配
   - 优化移动端体验

### 第二阶段：交互优化
1. **TaskFilter筛选器重新设计** (2天)
   - 改进筛选器视觉设计
   - 优化筛选逻辑

2. **动画效果系统** (2天)
   - 实现卡片动画
   - 添加状态变化过渡

3. **加载和错误状态** (1天)
   - 实现骨架屏
   - 添加错误边界

### 第三阶段：性能和可访问性
1. **性能优化** (2天)
   - 实现组件记忆化
   - 添加虚拟化支持

2. **可访问性支持** (1天)
   - 添加键盘导航
   - 优化屏幕阅读器支持

3. **测试和文档** (2天)
   - 编写单元测试
   - 完善组件文档

**总计预估：16个工作日**