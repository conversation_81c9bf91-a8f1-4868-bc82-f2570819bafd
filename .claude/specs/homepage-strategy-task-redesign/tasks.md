# 首页策略任务版块重新设计 - 任务分解

## 任务概述

基于需求规格书和设计规格书，将首页策略任务版块重新设计项目分解为可执行的编码任务。任务按照3个阶段进行，每个阶段都有明确的交付物和验收标准。

## 任务分解结构

### ✅ 第一阶段：核心组件重构 (6天) - 已完成

#### ✅ 任务1.1：TaskCard组件视觉层次重构 - 已完成
- **需求参考**: FR-01 任务卡片重新设计
- **设计参考**: TaskCard详细组件设计 (design.md第77行)
- **预估时间**: 2天
- **负责模块**: TaskCard组件核心结构
- **现有代码**: `src/components/Dashboard/TaskCard.tsx`

**子任务**:
- [x] 1.1.1 重构TaskCard组件结构，实现三层信息展示
  - 第一层级：股票信息 + 策略名称 + 状态指示器
  - 第二层级：盈亏显示（突出）+ 持仓信息
  - 第三层级：成本价格 + 当前价格 + 运行时长
- [x] 1.1.2 实现"弹出框风格"的卡片容器样式
  - 灰色主容器 (#f8f9fa) + 白色内容区域
  - 状态驱动的左边框色彩系统
  - hover效果和阴影提升
- [x] 1.1.3 优化卡片内容区域布局
  - 使用嵌套卡片设计增强视觉层次
  - 确保信息密度适中，避免拥挤感
- [x] 1.1.4 更新componentStyles.ts中的相关样式类
  - 扩展getCardClasses函数支持新的视觉效果
  - 添加状态边框和hover动画样式

#### ✅ 任务1.2：PnL盈亏显示重新设计 - 已完成
- **需求参考**: US-02 重点信息突出显示
- **设计参考**: TaskMetrics关键指标显示 (design.md第253行)
- **预估时间**: 1天
- **负责模块**: 盈亏信息展示组件
- **现有代码**: TaskCard组件内的盈亏计算逻辑

**子任务**:
- [x] 1.2.1 创建独立的PnLDisplay组件
  - 大字体显示盈亏金额
  - 趋势箭头图标 (▲ ▼)
  - 盈亏百分比显示
- [x] 1.2.2 实现盈亏区域的视觉强化
  - 盈利：绿色渐变背景 + 边框
  - 亏损：红色渐变背景 + 边框
  - 持平：灰色背景
- [x] 1.2.3 添加盈亏变化的动画效果
  - 数值变化时的高亮闪烁效果
  - 趋势图标的脉动动画
- [x] 1.2.4 优化盈亏计算逻辑的性能
  - 使用useMemo缓存计算结果
  - 避免不必要的重新计算

#### ✅ 任务1.3：TaskStatus状态指示器增强 - 已完成
- **需求参考**: US-01 快速状态总览
- **设计参考**: TaskStatus状态指示器重新设计 (design.md第185行)  
- **预估时间**: 1天
- **负责模块**: 状态显示和动画
- **现有代码**: `getStatusIndicatorClasses` 函数

**子任务**:
- [x] 1.3.1 更新状态指示器的视觉映射
  - 重新设计5种状态的颜色、图标和背景
  - 确保状态区分度和可识别性
- [x] 1.3.2 实现状态动画效果
  - 运行状态：脉动动画 (2s循环)
  - 错误状态：闪烁警告动画 (1s循环)
  - 其他状态：静态显示
- [x] 1.3.3 优化状态指示器的布局和尺寸
  - 合适的徽章大小和圆角
  - 与卡片头部的对齐和间距
- [x] 1.3.4 添加屏幕阅读器支持
  - aria-label属性提供状态描述
  - role="status"语义化标记

#### ✅ 任务1.4：TaskActions操作按钮简化 - 已完成
- **需求参考**: US-03 简化的操作界面
- **设计参考**: TaskActions操作按钮重新设计 (design.md第327行)
- **预估时间**: 1天
- **负责模块**: 操作按钮组件
- **现有代码**: TaskCard组件底部的按钮区域

**子任务**:
- [x] 1.4.1 重构操作按钮布局为两级结构
  - 主要操作：启动/暂停 + 详情查看（始终可见）
  - 次要操作：编辑、清仓、删除（下拉菜单）
- [x] 1.4.2 创建DropdownMenu组件
  - 可配置的菜单项支持
  - 禁用状态和危险操作样式
  - 点击外部区域关闭功能
- [x] 1.4.3 优化按钮的视觉效果和交互
  - hover和active状态的动画
  - 加载状态的指示器
  - 禁用状态的视觉反馈
- [x] 1.4.4 实现键盘导航支持
  - Tab键遍历所有可操作元素
  - Enter和Space键激活按钮
  - Escape键关闭下拉菜单

#### ✅ 任务1.5：响应式布局系统实现 - 已完成
- **需求参考**: US-04 响应式布局适配
- **设计参考**: 响应式布局设计 (design.md第421行)
- **预估时间**: 1天
- **负责模块**: 布局容器和网格系统
- **现有代码**: `layoutClasses.taskGrid` 样式

**子任务**:
- [x] 1.5.1 定义6个响应式断点的网格配置
  - xs(0px): 1列, sm(576px): 1列, md(768px): 2列
  - lg(992px): 2列, xl(1200px): 3列, xxl(1400px): 4列
- [x] 1.5.2 更新CSS网格布局样式
  - 使用CSS变量管理间距和内边距
  - 媒体查询实现断点适配
- [x] 1.5.3 优化卡片在不同屏幕的显示效果
  - 调整卡片最小/最大宽度
  - 确保内容在小屏幕上的可读性
- [x] 1.5.4 测试响应式布局在不同设备的效果
  - 手机竖屏/横屏、平板、桌面显示
  - 浏览器窗口缩放的适应性

### 🎨 第二阶段：交互体验优化 (5天) - 已完成

#### ✅ 任务2.1：TaskFilter筛选器重新设计 - 已完成
- **需求参考**: US-05 筛选和排序功能优化
- **设计参考**: TaskFilter筛选器重新设计 (design.md第380行)
- **预估时间**: 1.5天
- **负责模块**: 筛选器组件
- **现有代码**: TaskList内的筛选器逻辑

**子任务**:
- [x] 2.1.1 重新设计筛选按钮的视觉样式
  - 图标 + 文字 + 数量徽章的组合设计
  - 激活和非激活状态的明确区分
  - 与任务状态色彩保持一致
- [x] 2.1.2 优化筛选器的布局和响应式适配
  - 大屏幕：水平排列在右侧
  - 小屏幕：垂直排列或折叠菜单
- [x] 2.1.3 实现筛选状态的持久化
  - 使用localStorage保存用户的筛选偏好
  - 页面刷新后恢复上次的筛选状态
- [x] 2.1.4 添加筛选结果的动画过渡
  - 卡片显示/隐藏的淡入淡出效果
  - 网格重新排列的平滑过渡

#### ✅ 任务2.2：动画效果系统实现 - 已完成
- **需求参考**: FR-05 动画和交互效果
- **设计参考**: 交互设计规范 (design.md第493行)
- **预估时间**: 1.5天
- **负责模块**: 动画样式和组件过渡
- **现有代码**: 现有的动画类定义

**子任务**:
- [x] 2.2.1 实现卡片动画效果
  - 卡片载入的slideIn动画 (0.3s)
  - hover时的上浮和阴影增强效果
  - 状态变化时的边框颜色过渡
- [x] 2.2.2 添加数值变化的视觉反馈
  - 盈亏数值更新时的高亮闪烁
  - 新任务添加时的注意力引导动画
- [x] 2.2.3 创建可配置的动画系统
  - 动画开关设置（性能考虑）
  - 动画速度和缓动函数的统一管理
- [x] 2.2.4 确保动画的可访问性
  - respect用户的prefers-reduced-motion设置
  - 为动画敏感用户提供静态选项

#### ✅ 任务2.3：加载状态和错误处理 - 已完成
- **需求参考**: NFR-03 易用性
- **设计参考**: 加载状态设计 (design.md第547行)
- **预估时间**: 1天
- **负责模块**: 骨架屏和错误边界
- **现有代码**: 现有的LoadingOverlay组件

**子任务**:
- [x] 2.3.1 创建TaskCard骨架屏组件
  - 模拟真实卡片结构的占位元素
  - 流光动画效果 (skeleton-loading)
  - 不同加载状态的视觉区分
- [x] 2.3.2 实现TaskCard错误边界
  - 错误状态的友好界面展示
  - 重试按钮和错误信息显示
  - 错误上报和日志记录
- [x] 2.3.3 添加操作按钮的加载状态
  - 按钮点击后的loading指示器
  - 防止重复点击的UI反馈
  - 操作超时的处理机制
- [x] 2.3.4 优化空状态的用户体验
  - 更友好的图标和文案设计
  - 引导用户添加第一个任务
  - 筛选无结果时的提示优化

#### ✅ 任务2.4：用户体验细节优化 - 已完成
- **需求参考**: QL-01 用户体验
- **设计参考**: 多个设计细节
- **预估时间**: 1天
- **负责模块**: 交互细节和用户反馈
- **现有代码**: 各组件的交互逻辑

**子任务**:
- [x] 2.4.1 优化操作确认对话框
  - 危险操作（清仓、删除）的二次确认
  - 更清晰的操作后果说明
  - 一键撤销功能（适用场景）
- [x] 2.4.2 添加操作成功的反馈提示
  - Toast通知系统集成
  - 操作成功后的视觉确认
  - 批量操作的进度指示
- [x] 2.4.3 实现智能的界面更新
  - 实时数据更新不影响用户操作
  - 保持用户的筛选和滚动位置
  - 新数据的渐进式显示
- [x] 2.4.4 优化键盘和鼠标交互
  - 快捷键支持 (p暂停, l清仓等)
  - 右键菜单快速操作
  - 拖拽排序功能考虑

### ⚡ 第三阶段：性能与可访问性 (5天) - 已完成

#### ✅ 任务3.1：React性能优化
- **需求参考**: NFR-01, NFR-02 性能要求
- **设计参考**: 性能优化设计 (design.md第644行)
- **预估时间**: 1.5天
- **负责模块**: 组件渲染性能
- **现有代码**: 所有相关React组件

**子任务**:
- [x] 3.1.1 实现组件记忆化优化
  - TaskCard组件使用React.memo
  - 自定义比较函数优化重渲染判断
  - PnLDisplay等子组件的memo处理
- [x] 3.1.2 优化计算属性和事件处理器
  - 使用useMemo缓存复杂计算
  - useCallback稳定事件处理函数引用
  - 避免在render中创建新对象
- [x] 3.1.3 实现虚拟化列表支持
  - 大量任务时启用react-window
  - 动态行高和响应式列数支持
  - 性能阈值自动切换虚拟化
- [x] 3.1.4 内存使用和垃圾回收优化
  - 清理定时器和事件监听器
  - 避免内存泄漏的常见模式
  - 大对象的及时释放

#### ✅ 任务3.2：可访问性支持实现
- **需求参考**: NFR-04 可访问性
- **设计参考**: 可访问性设计 (design.md第786行)
- **预估时间**: 1天
- **负责模块**: 键盘导航和屏幕阅读器
- **现有代码**: 各UI组件

**子任务**:
- [x] 3.2.1 实现完整的键盘导航
  - Tab键遍历所有可交互元素
  - 方向键在卡片间导航
  - Enter/Space激活，Escape取消
- [x] 3.2.2 添加屏幕阅读器支持
  - 关键元素的aria-label标注
  - 状态变化的live region通知
  - 语义化的HTML结构使用
- [x] 3.2.3 确保颜色对比度符合WCAG标准
  - 文本和背景的对比度检查
  - 色盲用户的友好设计
  - 高对比度模式的支持
- [x] 3.2.4 焦点管理和视觉指示
  - 清晰的焦点边框样式
  - 逻辑化的焦点跳转顺序
  - 模态框的焦点陷阱

#### ✅ 任务3.3：兼容性和降级处理
- **需求参考**: NFR-05, NFR-06 兼容性要求
- **设计参考**: 兼容性考虑 (design.md第976行)
- **预估时间**: 1天
- **负责模块**: 浏览器兼容和性能降级
- **现有代码**: CSS样式和组件逻辑

**子任务**:
- [x] 3.3.1 实现CSS特性的渐进增强
  - Grid布局的Flexbox回退
  - CSS变量的默认值设置
  - 现代特性的@supports检测
- [x] 3.3.2 添加低性能设备的降级策略
  - 设备性能检测和自适应
  - 动画效果的可选关闭
  - 简化版本的自动切换
- [x] 3.3.3 确保现有功能的向后兼容
  - 不破坏现有的API接口
  - 保持数据格式的一致性
  - 平滑的功能迁移策略
- [x] 3.3.4 跨浏览器测试和修复
  - Chrome, Firefox, Safari, Edge测试
  - 移动端浏览器兼容性验证
  - 关键功能的最低版本支持

#### ✅ 任务3.4：测试体系建设
- **需求参考**: 整体项目质量保证
- **设计参考**: 测试设计 (design.md第874行)
- **预估时间**: 1.5天
- **负责模块**: 单元测试和集成测试
- **现有代码**: 现有测试框架

**子任务**:
- [x] 3.4.1 编写核心组件的单元测试
  - TaskCard组件的功能测试
  - PnLDisplay组件的计算逻辑测试
  - TaskFilter组件的筛选逻辑测试
- [x] 3.4.2 实现集成测试用例
  - TaskList组件的完整交互测试
  - 响应式布局的断点测试
  - 状态变化和数据流测试
- [x] 3.4.3 添加视觉回归测试
  - 关键界面的截图对比
  - 不同状态下的视觉一致性
  - 响应式布局的视觉验证
- [x] 3.4.4 性能测试和基准建立
  - 渲染性能的基准测试
  - 内存使用的监控测试
  - 大数据量下的性能验证

## 任务依赖关系

```mermaid
gantt
    title 首页策略任务版块重新设计项目时间线
    dateFormat  YYYY-MM-DD
    section 第一阶段
    TaskCard重构           :task1-1, 2024-01-01, 2d
    PnL显示设计           :task1-2, after task1-1, 1d
    状态指示器增强        :task1-3, after task1-1, 1d
    操作按钮简化          :task1-4, after task1-2, 1d
    响应式布局            :task1-5, after task1-3, 1d
    
    section 第二阶段
    筛选器重新设计        :task2-1, after task1-5, 1.5d
    动画效果系统          :task2-2, after task1-4, 1.5d
    加载状态处理          :task2-3, after task2-1, 1d
    用户体验优化          :task2-4, after task2-2, 1d
    
    section 第三阶段
    性能优化              :task3-1, after task2-3, 1.5d
    可访问性支持          :task3-2, after task2-4, 1d
    兼容性处理            :task3-3, after task3-2, 1d
    测试体系建设          :task3-4, after task3-1, 1.5d
```

## 技术实施要点

### 代码复用策略
1. **最大化现有代码利用**
   - 扩展现有的`componentStyles.ts`样式系统
   - 复用`useDashboard` Hook的数据逻辑
   - 保持现有的Task数据结构不变

2. **渐进式重构方法**
   - 先重构视觉层，再优化交互层
   - 保持功能的持续可用性
   - 分批次部署，降低回滚风险

### 关键技术决策
1. **样式方案**: 继续使用CSS-in-JS + TailwindCSS的混合方案
2. **状态管理**: 保持现有的Hook-based状态管理
3. **动画库**: 使用CSS animations，避免引入额外依赖
4. **测试策略**: Jest + Testing Library + Playwright

### 质量保证检查点

#### 第一阶段检查点
- [x] 新TaskCard组件在各种状态下正确显示
- [x] 响应式布局在所有目标断点正常工作
- [x] 现有功能完全保持兼容
- [x] 性能指标无明显退化

#### 第二阶段检查点  
- [x] 筛选器功能完整且用户友好
- [x] 动画效果流畅且可配置
- [x] 错误状态和加载状态处理完善
- [x] 用户体验显著改善

#### 第三阶段检查点
- [x] 性能优化目标达成 (渲染时间<100ms)
- [x] 可访问性测试通过 (WCAG 2.1 AA)
- [x] 跨浏览器兼容性验证通过
- [x] 测试覆盖率达到90%以上

## 风险缓解措施

### 高风险任务的备用方案
1. **任务1.1 TaskCard重构**: 如果复杂度过高，可分为两个迭代完成
2. **任务2.2 动画系统**: 如果性能影响明显，提供简化版本
3. **任务3.1 性能优化**: 如果虚拟化实现困难，先实现分页加载

### 质量风险控制
1. **每个任务完成后立即进行代码审查**
2. **关键功能改动前创建功能分支**
3. **性能回归监控和及时回滚机制**
4. **用户接受度测试和反馈收集**

## 项目里程碑

- **Day 6**: 第一阶段完成，核心UI重构交付
- **Day 11**: 第二阶段完成，交互体验优化交付  
- **Day 16**: 第三阶段完成，性能和可访问性优化交付
- **Day 18**: 项目收尾，文档完善和知识转移

**总预计时间：16个工作日 + 2天缓冲**