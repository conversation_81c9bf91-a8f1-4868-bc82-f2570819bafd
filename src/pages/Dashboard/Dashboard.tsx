import React from 'react';
import { useDashboard } from '../../hooks/useDashboard';
import { GlobalControls } from '../../components/Dashboard/GlobalControls';
import { TaskList } from '../../components/Dashboard/TaskList';
import { TaskConfigModal } from '../../components/Dashboard/TaskConfigModal';
import { TaskDetailsModal } from '../../components/Dashboard/TaskDetailsModal';
import { LoadingOverlay } from '../../components';

export const Dashboard: React.FC = () => {
    const {
        // 状态
        tasks,
        globalStatus,
        isLoading,
        selectedTask,
        showConfigModal,
        showDetailsModal,
        
        // 操作函数
        startAllTasks,
        stopAllTasks,
        addTask,
        updateTask,
        deleteTask,
        toggleTask,
        liquidateTask,
        
        // Modal 控制
        openConfigModal,
        closeConfigModal,
        openDetailsModal,
        closeDetailsModal,
        
        // 配置相关
        saveTaskConfig,
        editTask
    } = useDashboard();

    return (
        <div className="dashboard">
            {/* 全局控制区 */}
            <GlobalControls
                globalStatus={globalStatus}
                onAddTask={openConfigModal}
                onStartAll={startAllTasks}
                onStopAll={stopAllTasks}
                isLoading={isLoading}
            />

            {/* 任务列表区 */}
            <div className="dashboard-content">
                <TaskList
                    tasks={tasks}
                    onToggleTask={toggleTask}
                    onShowDetails={openDetailsModal}
                    onEditTask={editTask}
                    onDeleteTask={deleteTask}
                    onLiquidateTask={liquidateTask}
                />
            </div>

            {/* 任务配置模态窗口 */}
            {showConfigModal && (
                <TaskConfigModal
                    task={selectedTask}
                    onSave={saveTaskConfig}
                    onClose={closeConfigModal}
                />
            )}

            {/* 任务详细信息模态窗口 */}
            {showDetailsModal && selectedTask && (
                <TaskDetailsModal
                    task={selectedTask}
                    onClose={closeDetailsModal}
                />
            )}

            {/* 加载遮罩 */}
            <LoadingOverlay loading={isLoading} />
        </div>
    );
};