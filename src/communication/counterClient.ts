import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';

export interface CounterData {
    counter: number;
    timestamp: number;
}

export interface CounterResponse {
    counter?: number;
    message?: string;
    status?: string;
    timestamp?: number;
}

export class CounterClient {
    private isStarted = false;
    private listeners: Array<(data: CounterData) => void> = [];

    constructor() {
        this.setupEventListeners();
        this.syncServiceStatus(); // 同步服务状态
    }

    private async setupEventListeners() {
        // 监听来自 Python 的推送数据
        await listen('sidecar-message', (event: any) => {
            try {
                const message = event.payload;
                
                if (message.type === 'push' && message.source === 'counter') {
                    // 计数器数据推送 - 说明服务正在运行
                    if (!this.isStarted) {
                        console.log('🔍 Detected running service via push message, updating isStarted');
                        this.isStarted = true;
                    }
                    this.notifyListeners(message.data);
                } else if (message.type === 'push' && message.source === 'system') {
                    // 系统状态推送
                    console.log('System status:', message.data);
                }
            } catch (error) {
                console.error('Error handling sidecar message:', error);
            }
        });
    }

    private async syncServiceStatus() {
        try {
            console.log('🔍 Syncing service status...');
            const status = await this.getStatus();
            if (status && status.running) {
                this.isStarted = true;
                console.log('🔍 Service is running, set isStarted = true');
            } else {
                this.isStarted = false;
                console.log('🔍 Service is not running, set isStarted = false');
            }
        } catch (error) {
            console.error('🔍 Failed to sync service status:', error);
            this.isStarted = false;
        }
    }

    private notifyListeners(data: CounterData) {
        this.listeners.forEach(listener => {
            try {
                listener(data);
            } catch (error) {
                console.error('Error in counter listener:', error);
            }
        });
    }

    /**
     * 启动计数器服务
     */
    async start(): Promise<void> {
        if (this.isStarted) {
            console.log('Counter service already started');
            return;
        }

        try {
            await invoke('start_service', { serviceName: 'counter' });
            this.isStarted = true;
            console.log('Counter service started successfully');
        } catch (error) {
            console.error('Failed to start counter service:', error);
            throw error;
        }
    }

    /**
     * 停止计数器服务
     */
    async stop(): Promise<void> {
        console.log('🔍 CounterClient.stop() called, isStarted:', this.isStarted);
        
        if (!this.isStarted) {
            console.log('🔍 Counter service not started, skipping stop');
            return;
        }

        try {
            console.log('🔍 Calling invoke(stop_service) with serviceName: counter...');
            await invoke('stop_service', { serviceName: 'counter' });
            this.isStarted = false;
            console.log('✅ Counter service stopped successfully');
        } catch (error) {
            console.error('❌ Failed to stop counter service:', error);
            throw error;
        }
    }

    /**
     * 获取服务状态
     */
    async getStatus(): Promise<any> {
        try {
            return await invoke('get_service_status', { serviceName: 'counter' });
        } catch (error) {
            console.error('Failed to get service status:', error);
            throw error;
        }
    }

    /**
     * 发送 ping 命令
     */
    async ping(): Promise<CounterResponse> {
        try {
            const response = await invoke('send_service_command', {
                serviceName: 'counter',
                action: 'ping',
                params: {}
            });
            return response as CounterResponse;
        } catch (error) {
            console.error('Failed to ping counter service:', error);
            throw error;
        }
    }

    /**
     * 获取当前计数器值
     */
    async getCounter(): Promise<CounterResponse> {
        try {
            const response = await invoke('send_service_command', {
                serviceName: 'counter',
                action: 'get_counter',
                params: {}
            });
            return response as CounterResponse;
        } catch (error) {
            console.error('Failed to get counter:', error);
            throw error;
        }
    }

    /**
     * 重置计数器
     */
    async resetCounter(): Promise<CounterResponse> {
        try {
            const response = await invoke('send_service_command', {
                serviceName: 'counter',
                action: 'reset_counter',
                params: {}
            });
            return response as CounterResponse;
        } catch (error) {
            console.error('Failed to reset counter:', error);
            throw error;
        }
    }

    /**
     * 添加计数器数据监听器
     */
    addListener(listener: (data: CounterData) => void): void {
        this.listeners.push(listener);
    }

    /**
     * 移除计数器数据监听器
     */
    removeListener(listener: (data: CounterData) => void): void {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 移除所有监听器
     */
    removeAllListeners(): void {
        this.listeners = [];
    }

    /**
     * 检查服务是否已启动
     */
    get started(): boolean {
        return this.isStarted;
    }
}

// 导出单例实例
export const counterClient = new CounterClient();
