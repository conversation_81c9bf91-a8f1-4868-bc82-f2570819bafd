import React, { useMemo, useState } from 'react';
import { Task } from '../../types';
import { 
  getCardClasses, 
  getStatusIndicatorClasses, 
  getStatusIndicatorStyle,
  getStatusIcon,
  getStatusText,
  getStatusColor,
  getButtonClasses, 
  textClasses, 
  layoutClasses
} from '../../styles/componentStyles';
import { PnLDisplay, usePnLHighlight } from './PnLDisplay';
import { DropdownMenu } from './DropdownMenu';
import { AnimatedComponent, ValueHighlight, useAnimation, animationSystem } from './AnimationSystem';

interface TaskCardProps {
    task: Task;
    onToggle: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEdit: (taskId: string) => void;
    onDelete: (taskId: string) => void;
    onLiquidate: (taskId: string) => void;
}

export const TaskCard: React.FC<TaskCardProps> = ({
    task,
    onToggle,
    onShowDetails,
    onEdit,
    onDelete,
    onLiquidate
}) => {
    const [isLoading, setIsLoading] = React.useState(false);
    const [isVisible, setIsVisible] = React.useState(false);
    const [showDropdown, setShowDropdown] = useState(false);

    // 组件挂载时触发渐入动画
    React.useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 100);
        return () => clearTimeout(timer);
    }, []);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    const getStatusText = (status: Task['status']) => {
        switch (status) {
            case 'running':
                return '运行中';
            case 'paused':
                return '已暂停';
            case 'error':
                return '错误';
            case 'stopped':
                return '已停止';
            case 'liquidated':
                return '已清仓';
            default:
                return status;
        }
    };

    const handleLiquidate = async () => {
        if (window.confirm(`确定要清仓 "${task.name}" 的所有持仓吗？`)) {
            setIsLoading(true);
            try {
                await new Promise(resolve => setTimeout(resolve, 300)); // 模拟网络延迟
                onLiquidate(task.id);
            } finally {
                setIsLoading(false);
            }
        }
    };

    const handleDelete = async () => {
        if (window.confirm(`确定要删除任务 "${task.name}" 吗？`)) {
            setIsLoading(true);
            try {
                await new Promise(resolve => setTimeout(resolve, 300)); // 模拟网络延迟
                onDelete(task.id);
            } finally {
                setIsLoading(false);
            }
        }
    };

    const handleToggle = async () => {
        setIsLoading(true);
        try {
            await new Promise(resolve => setTimeout(resolve, 200)); // 模拟网络延迟
            onToggle(task.id);
        } finally {
            setIsLoading(false);
        }
    };


    const formatTime = (date: Date) => {
        return new Intl.DateTimeFormat('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).format(date);
    };

    // 获取盈亏样式类名（保持兼容性）
    const getPnlClass = (pnl: number) => {
        if (pnl > 0) return textClasses.profit;
        if (pnl < 0) return textClasses.loss;
        return textClasses.neutral;
    };


    // 按钮样式映射
    const getButtonStyle = (variant: string) => {
        const baseStyle = {
            fontWeight: '500',
            borderRadius: '4px',
            border: 'none',
            padding: '6px 12px',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '4px'
        };
        
        switch (variant) {
            case 'success':
                return { ...baseStyle, backgroundColor: '#28a745', color: 'white' };
            case 'warning':
                return { ...baseStyle, backgroundColor: '#ffc107', color: '#212529' };
            case 'danger':
                return { ...baseStyle, backgroundColor: '#dc3545', color: 'white' };
            case 'secondary':
                return { ...baseStyle, backgroundColor: '#6c757d', color: 'white' };
            case 'info':
                return { ...baseStyle, backgroundColor: '#17a2b8', color: 'white' };
            default:
                return { ...baseStyle, backgroundColor: '#007bff', color: 'white' };
        }
    };

    // 获取运行时长
    const getRunningDuration = () => {
        const now = new Date();
        const diffMs = now.getTime() - task.createdAt.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (diffHours > 0) {
            return `${diffHours}小时${diffMinutes}分钟`;
        } else {
            return `${diffMinutes}分钟`;
        }
    };


    // 使用盈亏高亮效果
    const shouldHighlight = usePnLHighlight(task.pnl);
    
    // 使用动画系统
    const cardAnimation = useAnimation('fadeInUp', isVisible, {
        duration: animationSystem.getDuration(300),
        delay: 100
    });
    const pnlPercentage = useMemo(() => {
        if (!task.avgCost || task.position === 0) return 0;
        return (task.pnl / (task.avgCost * task.position)) * 100;
    }, [task.pnl, task.avgCost, task.position]);

    // 当前价格计算（优化性能）
    const currentPrice = useMemo(() => {
        if (!task.avgCost || task.position === 0) return null;
        return task.avgCost + (task.pnl / task.position);
    }, [task.avgCost, task.pnl, task.position]);

    return (
        <AnimatedComponent
            animation="scaleIn"
            trigger={isVisible}
            delay={0}
            config={{ duration: animationSystem.getDuration(300) }}
        >
            <div 
            className="task-card-container"
            style={{
                background: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '8px',
                padding: '16px',
                position: 'relative',
                transition: 'all 0.2s ease',
                borderLeft: `4px solid ${getStatusColor(task.status)}`,
                opacity: isVisible ? 1 : 0,
                transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
                animation: isVisible ? 'cardSlideIn 0.3s ease-out' : 'none'
            }}
            onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
            }}
        >
            
            {/* 加载覆盖层 */}
            {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
                    <div className="flex items-center gap-2 text-gray-600">
                        <div className="animate-spin w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full"></div>
                        <span className="text-sm">处理中...</span>
                    </div>
                </div>
            )}

            {/* 第一层级：核心识别信息 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                <div style={{ flex: 1, minWidth: 0 }}>
                    <h3 style={{ 
                        margin: 0, 
                        fontSize: '18px', 
                        fontWeight: 'bold', 
                        color: '#495057',
                        marginBottom: '4px',
                        transition: 'color 0.2s ease'
                    }}>
                        {task.stockName} ({task.stockCode})
                    </h3>
                    <p style={{ 
                        margin: 0, 
                        fontSize: '14px', 
                        color: '#666',
                        fontWeight: '500'
                    }}>
                        {task.strategyName}
                    </p>
                </div>
                <div 
                    style={{
                        ...getStatusIndicatorStyle(task.status, { animated: true, size: 'md' }),
                        cursor: 'pointer'
                    }}
                    title={`任务状态：${getStatusText(task.status)}`}
                >
                    <div 
                        style={{
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            backgroundColor: 'currentColor',
                            animation: task.status === 'running' ? 'statusPulse 2s infinite' 
                                     : task.status === 'error' ? 'errorBlink 1s infinite alternate'
                                     : 'none'
                        }}
                    />
                    <span>{getStatusText(task.status)}</span>
                </div>
            </div>

            {/* 内容卡片区域 - 弹出框风格 */}
            <div 
                style={{
                    background: 'white',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '12px',
                    marginTop: '8px',
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                }}
            >
                {/* 第二层级：关键决策信息 - 使用新的PnLDisplay组件 */}
                <ValueHighlight
                    value={task.pnl}
                    highlightColor="rgba(255, 235, 59, 0.3)"
                    highlightDuration={animationSystem.getDuration(1000)}
                >
                    <PnLDisplay
                        pnl={task.pnl}
                        pnlPercentage={pnlPercentage}
                        size="medium"
                        showTrend={false}
                    />
                </ValueHighlight>

                {/* 持仓信息 */}
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '12px' }}>
                    <div>
                        <div style={{ fontSize: '11px', color: '#6c757d', marginBottom: '4px' }}>持仓数量</div>
                        <div style={{ 
                            fontSize: '14px', 
                            fontWeight: '500',
                            color: task.position > 0 ? '#28a745' : '#6c757d'
                        }}>
                            {task.position > 0 ? `${task.position.toLocaleString()} 股` : '空仓'}
                        </div>
                    </div>
                    <div>
                        <div style={{ fontSize: '11px', color: '#6c757d', marginBottom: '4px' }}>成本价</div>
                        <div style={{ 
                            fontSize: '14px', 
                            fontWeight: '500',
                            color: task.avgCost ? '#fd7e14' : '#6c757d'
                        }}>
                            {task.avgCost ? formatCurrency(task.avgCost) : '-'}
                        </div>
                    </div>
                </div>

                {/* 第三层级：辅助信息 */}
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '12px', color: '#868e96' }}>
                    <div>
                        <span>当前价格: </span>
                        <span style={{ fontWeight: '500' }}>
                            {currentPrice ? formatCurrency(currentPrice) : '-'}
                        </span>
                    </div>
                    <div>
                        <span>运行时长: </span>
                        <span style={{ fontWeight: '500' }}>{getRunningDuration()}</span>
                    </div>
                </div>
            </div>

                {/* 操作按钮区域 - 简化版本 */}
            <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: '8px',
                paddingTop: '12px',
                borderTop: '1px solid #f0f0f0',
                marginTop: '12px'
            }}>
                {/* 主要操作（始终可见） */}
                <div style={{ display: 'flex', gap: '8px' }}>
                    <button 
                        style={{
                            ...getButtonStyle(task.status === 'running' ? 'warning' : 'success'),
                            transition: 'all 0.2s ease',
                            opacity: isLoading ? 0.5 : 1,
                            cursor: (task.status === 'error' || isLoading) ? 'not-allowed' : 'pointer'
                        }}
                        onClick={handleToggle}
                        disabled={task.status === 'error' || isLoading}
                        onMouseEnter={(e) => {
                            if (!e.currentTarget.disabled) {
                                e.currentTarget.style.transform = 'scale(1.05)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'scale(1)';
                        }}
                    >
                        <span style={{ marginRight: '4px' }}>
                            {isLoading ? '⏳' : task.status === 'running' ? '⏸️' : '▶️'}
                        </span>
                        {task.status === 'running' ? '暂停' : '启动'}
                    </button>
                    
                    <button 
                        style={{
                            ...getButtonStyle('info'),
                            transition: 'all 0.2s ease',
                            opacity: isLoading ? 0.5 : 1,
                            cursor: isLoading ? 'not-allowed' : 'pointer'
                        }}
                        onClick={() => onShowDetails(task.id)}
                        disabled={isLoading}
                        onMouseEnter={(e) => {
                            if (!e.currentTarget.disabled) {
                                e.currentTarget.style.transform = 'scale(1.05)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'scale(1)';
                        }}
                    >
                        <span style={{ marginRight: '4px' }}>📊</span>
                        详情
                    </button>
                </div>

                {/* 次要操作（下拉菜单） */}
                <DropdownMenu
                    isOpen={showDropdown}
                    onToggle={setShowDropdown}
                    size="sm"
                    placement="bottom-right"
                    items={[
                        {
                            label: '编辑',
                            icon: '✏️',
                            action: () => onEdit(task.id),
                            disabled: isLoading,
                            description: '编辑任务配置'
                        },
                        ...(task.position > 0 ? [{
                            label: '清仓',
                            icon: '🚨',
                            action: handleLiquidate,
                            disabled: task.status !== 'running' || isLoading,
                            style: 'danger' as const,
                            description: task.status !== 'running' ? '只有运行中的任务可以清仓' : '清仓所有持仓'
                        }] : []),
                        {
                            label: '删除',
                            icon: '🗑️',
                            action: handleDelete,
                            disabled: task.status === 'running' || isLoading,
                            style: 'danger' as const,
                            description: task.status === 'running' ? '运行中的任务无法删除' : '删除任务'
                        }
                    ]}
                    trigger={
                        <button
                            style={{
                                padding: '4px 8px',
                                fontSize: '12px',
                                backgroundColor: '#6c757d',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                opacity: isLoading ? 0.5 : 1
                            }}
                            disabled={isLoading}
                            onMouseEnter={(e) => {
                                if (!e.currentTarget.disabled) {
                                    e.currentTarget.style.backgroundColor = '#5a6268';
                                    e.currentTarget.style.transform = 'scale(1.05)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = '#6c757d';
                                e.currentTarget.style.transform = 'scale(1)';
                            }}
                        >
                            ⋯
                        </button>
                    }
                />
            </div>
            </div>
        </AnimatedComponent>
    );
};

// CSS 动画样式（添加到全局CSS或组件样式中）
const styleSheet = document.createElement('style');
styleSheet.textContent = `
    @keyframes cardSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes statusPulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.05); }
    }
    
    @keyframes errorBlink {
        0% { background-color: #fecaca; }
        100% { background-color: #fca5a5; }
    }
    
    @keyframes trendPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }
    
    @keyframes valueHighlight {
        0% { background-color: rgba(255, 235, 59, 0.3); }
        100% { background-color: transparent; }
    }
`;
if (!document.head.querySelector('style[data-task-card-styles]')) {
    styleSheet.setAttribute('data-task-card-styles', 'true');
    document.head.appendChild(styleSheet);
}