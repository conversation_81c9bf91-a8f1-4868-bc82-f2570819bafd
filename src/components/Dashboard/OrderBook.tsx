import React from 'react';
import { MockOrderBook, MockOrderBookEntry } from '../../types';

interface OrderBookProps {
    orderBook: MockOrderBook;
}

export const OrderBook: React.FC<OrderBookProps> = ({ orderBook }) => {
    const formatVolume = (volume: number) => {
        if (volume >= 10000) {
            return `${(volume / 10000).toFixed(1)}万`;
        }
        return volume.toLocaleString();
    };

    const formatPrice = (price: number) => {
        return price.toFixed(2);
    };

    return (
        <div className="order-book">
            <div className="order-book-header">
                <h4>十档行情 - {orderBook.stockCode}</h4>
                <span className="update-time">
                    {orderBook.timestamp.toLocaleTimeString()}
                </span>
            </div>
            
            <div className="order-book-content">
                <div className="order-book-table">
                    <div className="table-header">
                        <div className="col-brokers">经纪商</div>
                        <div className="col-volume">数量</div>
                        <div className="col-price">价格</div>
                        <div className="col-volume">数量</div>
                        <div className="col-brokers">经纪商</div>
                    </div>
                    
                    <div className="table-body">
                        {Array.from({ length: 10 }, (_, index) => {
                            const askEntry = orderBook.asks[index];
                            const bidEntry = orderBook.bids[index];
                            
                            return (
                                <div key={index} className="table-row">
                                    {/* 买盘经纪商 */}
                                    <div className="col-brokers bid-brokers">
                                        {bidEntry?.brokerIds.slice(0, 3).join(' ') || '-'}
                                    </div>
                                    
                                    {/* 买盘数量 */}
                                    <div className="col-volume bid-volume">
                                        {bidEntry ? formatVolume(bidEntry.volume) : '-'}
                                    </div>
                                    
                                    {/* 买盘价格 */}
                                    <div className="col-price bid-price">
                                        {bidEntry ? formatPrice(bidEntry.price) : '-'}
                                    </div>
                                    
                                    {/* 卖盘价格 */}
                                    <div className="col-price ask-price">
                                        {askEntry ? formatPrice(askEntry.price) : '-'}
                                    </div>
                                    
                                    {/* 卖盘数量 */}
                                    <div className="col-volume ask-volume">
                                        {askEntry ? formatVolume(askEntry.volume) : '-'}
                                    </div>
                                    
                                    {/* 卖盘经纪商 */}
                                    <div className="col-brokers ask-brokers">
                                        {askEntry?.brokerIds.slice(0, 3).join(' ') || '-'}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};