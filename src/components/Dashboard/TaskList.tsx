import React, { useState, useMemo } from 'react';
import { Task } from '../../types';
import { TaskCard } from './TaskCard';
import { TaskFilter, FilterStatus, FilterStats, useFilterPersistence } from './TaskFilter';
import { StaggeredAnimation, AnimatedComponent, animationSystem } from './AnimationSystem';
import { 
  layoutClasses, 
  getButtonClasses, 
  textClasses
} from '../../styles/componentStyles';

interface TaskListProps {
    tasks: Task[];
    onToggleTask: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEditTask: (taskId: string) => void;
    onDeleteTask: (taskId: string) => void;
    onLiquidateTask: (taskId: string) => void;
}

type FilterStatus = 'all' | 'running' | 'paused' | 'stopped' | 'error' | 'liquidated';

export const TaskList: React.FC<TaskListProps> = ({
    tasks,
    onToggleTask,
    onShowDetails,
    onEditTask,
    onDeleteTask,
    onLiquidateTask
}) => {
    // 使用持久化的筛选状态
    const [filterStatus, setFilterStatus] = useFilterPersistence('all');

    // 计算各状态的任务数量
    const statusCounts = useMemo(() => {
        return {
            all: tasks.length,
            running: tasks.filter(t => t.status === 'running').length,
            paused: tasks.filter(t => t.status === 'paused').length,
            stopped: tasks.filter(t => t.status === 'stopped').length,
            error: tasks.filter(t => t.status === 'error').length,
            liquidated: tasks.filter(t => t.status === 'liquidated').length
        };
    }, [tasks]);

    // 根据筛选状态过滤任务
    const filteredTasks = useMemo(() => {
        if (filterStatus === 'all') {
            return tasks;
        }
        return tasks.filter(task => task.status === filterStatus);
    }, [tasks, filterStatus]);

    // 处理筛选点击（保持兼容性）
    const handleFilterClick = (status: FilterStatus) => {
        setFilterStatus(status);
    };


    if (tasks.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[400px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                <div className="text-center space-y-4">
                    <div className="text-6xl">📋</div>
                    <h3 className={`${textClasses.title} text-xl`}>暂无策略任务</h3>
                    <p className={`${textClasses.subtitle} max-w-md`}>
                        点击"添加新任务"开始创建您的第一个量化交易策略
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* 头部标题和筛选器 */}
            <div className={layoutClasses.pageHeader}>
                <div>
                    <h2 className={`${textClasses.title} text-xl md:text-2xl mb-1`}>
                        策略任务
                    </h2>
                    <p className={`${textClasses.subtitle} text-sm md:text-base`}>
                        共 {statusCounts.all} 项任务，{statusCounts.running} 项运行中
                    </p>
                    <FilterStats 
                        totalTasks={statusCounts.all}
                        filteredTasks={filteredTasks.length}
                        currentFilter={filterStatus}
                    />
                </div>
                
                {/* 新的筛选器组件 */}
                <TaskFilter
                    currentFilter={filterStatus}
                    statusCounts={statusCounts}
                    onFilterChange={handleFilterClick}
                    animated={true}
                    size="md"
                    variant="pills"
                />
            </div>

            {/* 任务网格 - 使用交错动画 */}
            <div className={layoutClasses.taskGrid}>
                {filteredTasks.map((task, index) => (
                    <AnimatedComponent
                        key={task.id}
                        animation="fadeInUp"
                        trigger={true}
                        delay={index * 50}
                        config={{ 
                            duration: animationSystem.getDuration(300),
                            fillMode: 'both'
                        }}
                    >
                        <TaskCard
                            task={task}
                            onToggle={onToggleTask}
                            onShowDetails={onShowDetails}
                            onEdit={onEditTask}
                            onDelete={onDeleteTask}
                            onLiquidate={onLiquidateTask}
                        />
                    </AnimatedComponent>
                ))}
            </div>

            {/* 无匹配任务的空状态 */}
            {filteredTasks.length === 0 && filterStatus !== 'all' && (
                <div className="flex flex-col items-center justify-center min-h-[300px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                    <div className="text-center space-y-4">
                        <div className="text-5xl">🔍</div>
                        <h3 className={`${textClasses.title} text-lg`}>无匹配任务</h3>
                        <p className={`${textClasses.subtitle} max-w-md`}>
                            当前筛选条件下没有找到相关任务
                        </p>
                        <button 
                            className={`${getButtonClasses('secondary')} hover:scale-105 active:scale-95 transition-all duration-200`}
                            onClick={() => handleFilterClick('all')}
                        >
                            <span className="mr-2">📊</span>
                            查看全部任务
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};