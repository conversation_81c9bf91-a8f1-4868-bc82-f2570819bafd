import { useState, useEffect, useRef, useCallback } from 'react';
import { Task } from '../../types';

// 智能数据更新Hook - 保持用户操作状态
export const useSmartDataUpdate = <T extends { id: string }>(
    initialData: T[],
    fetchData: () => Promise<T[]>,
    interval: number = 5000
) => {
    const [data, setData] = useState(initialData);
    const [isUpdating, setIsUpdating] = useState(false);
    const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
    const userInteractionRef = useRef(false);
    const updateTimeoutRef = useRef<NodeJS.Timeout>();

    // 标记用户交互
    const markUserInteraction = useCallback(() => {
        userInteractionRef.current = true;
        // 5秒后重置交互标记
        setTimeout(() => {
            userInteractionRef.current = false;
        }, 5000);
    }, []);

    // 智能更新数据
    const updateData = useCallback(async () => {
        // 如果用户正在交互，延迟更新
        if (userInteractionRef.current) {
            updateTimeoutRef.current = setTimeout(updateData, 2000);
            return;
        }

        setIsUpdating(true);
        try {
            const newData = await fetchData();
            
            setData(currentData => {
                // 智能合并：保留用户正在操作的项目状态
                const mergedData = newData.map(newItem => {
                    const existingItem = currentData.find(item => item.id === newItem.id);
                    
                    // 如果项目正在被用户操作，保留某些状态
                    if (existingItem && userInteractionRef.current) {
                        // 这里可以根据需要保留特定字段
                        return {
                            ...newItem,
                            // 保留可能正在编辑的字段
                        };
                    }
                    
                    return newItem;
                });
                
                return mergedData;
            });
            
            setLastUpdateTime(Date.now());
        } catch (error) {
            console.error('Failed to update data:', error);
        } finally {
            setIsUpdating(false);
        }
    }, [fetchData]);

    // 定期更新
    useEffect(() => {
        const intervalId = setInterval(updateData, interval);
        return () => clearInterval(intervalId);
    }, [updateData, interval]);

    // 手动刷新
    const refresh = useCallback(async () => {
        userInteractionRef.current = false;
        await updateData();
    }, [updateData]);

    return {
        data,
        isUpdating,
        lastUpdateTime,
        refresh,
        markUserInteraction
    };
};

// 乐观更新Hook
export const useOptimisticUpdate = <T extends { id: string }>(
    data: T[],
    updateFn: (id: string, updates: Partial<T>) => Promise<T>
) => {
    const [optimisticData, setOptimisticData] = useState(data);
    const [pendingUpdates, setPendingUpdates] = useState<Map<string, Partial<T>>>(new Map());

    // 同步外部数据变化
    useEffect(() => {
        setOptimisticData(data);
    }, [data]);

    const updateOptimistically = useCallback(async (
        id: string,
        updates: Partial<T>,
        onSuccess?: (result: T) => void,
        onError?: (error: Error) => void
    ) => {
        // 立即更新UI（乐观更新）
        setOptimisticData(current =>
            current.map(item =>
                item.id === id ? { ...item, ...updates } : item
            )
        );

        // 记录待处理的更新
        setPendingUpdates(prev => new Map(prev).set(id, updates));

        try {
            // 执行实际更新
            const result = await updateFn(id, updates);
            
            // 更新成功，用服务器返回的数据更新
            setOptimisticData(current =>
                current.map(item =>
                    item.id === id ? result : item
                )
            );
            
            onSuccess?.(result);
        } catch (error) {
            // 更新失败，回滚乐观更新
            setOptimisticData(current =>
                current.map(item => {
                    if (item.id === id) {
                        // 找到原始数据并回滚
                        const originalItem = data.find(d => d.id === id);
                        return originalItem || item;
                    }
                    return item;
                })
            );
            
            onError?.(error as Error);
        } finally {
            // 清除待处理标记
            setPendingUpdates(prev => {
                const newMap = new Map(prev);
                newMap.delete(id);
                return newMap;
            });
        }
    }, [data, updateFn]);

    const isUpdating = (id: string) => pendingUpdates.has(id);

    return {
        data: optimisticData,
        updateOptimistically,
        isUpdating,
        pendingUpdates: Array.from(pendingUpdates.keys())
    };
};

// 增量更新Hook
export const useIncrementalUpdate = <T extends { id: string; updatedAt: string }>(
    initialData: T[],
    fetchUpdates: (since: string) => Promise<T[]>
) => {
    const [data, setData] = useState(initialData);
    const [lastSyncTime, setLastSyncTime] = useState(new Date().toISOString());
    const [isSyncing, setIsSyncing] = useState(false);

    const syncUpdates = useCallback(async () => {
        setIsSyncing(true);
        try {
            const updates = await fetchUpdates(lastSyncTime);
            
            if (updates.length > 0) {
                setData(currentData => {
                    const dataMap = new Map(currentData.map(item => [item.id, item]));
                    
                    // 合并更新
                    updates.forEach(update => {
                        dataMap.set(update.id, update);
                    });
                    
                    return Array.from(dataMap.values()).sort((a, b) =>
                        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
                    );
                });
                
                // 更新同步时间
                const latestUpdate = updates.reduce((latest, current) =>
                    new Date(current.updatedAt) > new Date(latest.updatedAt) ? current : latest
                );
                setLastSyncTime(latestUpdate.updatedAt);
            }
        } catch (error) {
            console.error('Failed to sync updates:', error);
        } finally {
            setIsSyncing(false);
        }
    }, [lastSyncTime, fetchUpdates]);

    return {
        data,
        syncUpdates,
        isSyncing,
        lastSyncTime
    };
};

// 防抖更新Hook
export const useDebouncedUpdate = <T>(
    value: T,
    delay: number = 500
) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    const [isPending, setIsPending] = useState(false);

    useEffect(() => {
        setIsPending(true);
        const timer = setTimeout(() => {
            setDebouncedValue(value);
            setIsPending(false);
        }, delay);

        return () => {
            clearTimeout(timer);
        };
    }, [value, delay]);

    return {
        debouncedValue,
        isPending
    };
};

// 批量更新Hook
export const useBatchUpdate = <T extends { id: string }>(
    updateFn: (updates: Array<{ id: string; data: Partial<T> }>) => Promise<void>
) => {
    const [pendingUpdates, setPendingUpdates] = useState<Map<string, Partial<T>>>(new Map());
    const [isBatching, setIsBatching] = useState(false);
    const batchTimeoutRef = useRef<NodeJS.Timeout>();

    const addToBatch = useCallback((id: string, updates: Partial<T>) => {
        setPendingUpdates(prev => {
            const newMap = new Map(prev);
            const existing = newMap.get(id) || {};
            newMap.set(id, { ...existing, ...updates });
            return newMap;
        });

        // 清除之前的超时
        if (batchTimeoutRef.current) {
            clearTimeout(batchTimeoutRef.current);
        }

        // 设置新的超时，500ms后执行批量更新
        batchTimeoutRef.current = setTimeout(() => {
            executeBatch();
        }, 500);
    }, []);

    const executeBatch = useCallback(async () => {
        if (pendingUpdates.size === 0) return;

        setIsBatching(true);
        const updates = Array.from(pendingUpdates.entries()).map(([id, data]) => ({
            id,
            data
        }));

        try {
            await updateFn(updates);
            setPendingUpdates(new Map());
        } catch (error) {
            console.error('Batch update failed:', error);
        } finally {
            setIsBatching(false);
        }
    }, [pendingUpdates, updateFn]);

    const clearBatch = useCallback(() => {
        if (batchTimeoutRef.current) {
            clearTimeout(batchTimeoutRef.current);
        }
        setPendingUpdates(new Map());
    }, []);

    return {
        addToBatch,
        executeBatch,
        clearBatch,
        pendingCount: pendingUpdates.size,
        isBatching
    };
};