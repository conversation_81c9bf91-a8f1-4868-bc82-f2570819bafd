import { useEffect, useRef, useState, useCallback } from 'react';

// 性能监控Hook
export const usePerformanceMonitor = (componentName: string) => {
    const renderCount = useRef(0);
    const renderStartTime = useRef<number>(0);
    const [metrics, setMetrics] = useState({
        renderCount: 0,
        lastRenderTime: 0,
        averageRenderTime: 0,
        peakRenderTime: 0
    });

    useEffect(() => {
        renderCount.current += 1;
        const renderEndTime = performance.now();
        const renderTime = renderEndTime - renderStartTime.current;

        setMetrics(prev => ({
            renderCount: renderCount.current,
            lastRenderTime: renderTime,
            averageRenderTime: (prev.averageRenderTime * (renderCount.current - 1) + renderTime) / renderCount.current,
            peakRenderTime: Math.max(prev.peakRenderTime, renderTime)
        }));

        if (process.env.NODE_ENV === 'development' && renderTime > 16.67) {
            console.warn(`[Performance] ${componentName} render took ${renderTime.toFixed(2)}ms (target: 16.67ms)`);
        }
    });

    // 在渲染开始时记录时间
    renderStartTime.current = performance.now();

    return metrics;
};

// 内存监控Hook
export const useMemoryMonitor = () => {
    const [memoryInfo, setMemoryInfo] = useState({
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0,
        percentUsed: 0
    });

    useEffect(() => {
        if ('memory' in performance) {
            const updateMemoryInfo = () => {
                const memory = (performance as any).memory;
                const percentUsed = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                
                setMemoryInfo({
                    usedJSHeapSize: memory.usedJSHeapSize,
                    totalJSHeapSize: memory.totalJSHeapSize,
                    jsHeapSizeLimit: memory.jsHeapSizeLimit,
                    percentUsed
                });

                // 内存使用超过80%时发出警告
                if (percentUsed > 80) {
                    console.warn(`[Memory] High memory usage: ${percentUsed.toFixed(2)}%`);
                }
            };

            updateMemoryInfo();
            const interval = setInterval(updateMemoryInfo, 5000);
            return () => clearInterval(interval);
        }
    }, []);

    return memoryInfo;
};

// 自动清理Hook - 防止内存泄漏
export const useAutoCleanup = () => {
    const cleanupFunctions = useRef<(() => void)[]>([]);

    const registerCleanup = useCallback((cleanup: () => void) => {
        cleanupFunctions.current.push(cleanup);
    }, []);

    useEffect(() => {
        return () => {
            cleanupFunctions.current.forEach(cleanup => cleanup());
            cleanupFunctions.current = [];
        };
    }, []);

    return { registerCleanup };
};

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number = 500): T => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => clearTimeout(timer);
    }, [value, delay]);

    return debouncedValue;
};

// 节流Hook
export const useThrottle = <T>(value: T, limit: number = 500): T => {
    const [throttledValue, setThrottledValue] = useState(value);
    const lastRun = useRef(Date.now());

    useEffect(() => {
        const handler = setTimeout(() => {
            if (Date.now() - lastRun.current >= limit) {
                setThrottledValue(value);
                lastRun.current = Date.now();
            }
        }, limit - (Date.now() - lastRun.current));

        return () => clearTimeout(handler);
    }, [value, limit]);

    return throttledValue;
};

// 延迟加载Hook
export const useLazyLoad = <T>(
    loader: () => Promise<T>,
    dependencies: any[] = []
) => {
    const [data, setData] = useState<T | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const load = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const result = await loader();
            setData(result);
        } catch (err) {
            setError(err as Error);
        } finally {
            setIsLoading(false);
        }
    }, dependencies);

    return { data, isLoading, error, load };
};

// 性能降级Hook
export const usePerformanceDegradation = () => {
    const [isLowPerformance, setIsLowPerformance] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState({
        deviceMemory: 8,
        hardwareConcurrency: 4,
        connection: 'unknown'
    });

    useEffect(() => {
        // 检测设备性能
        const navigator = window.navigator as any;
        
        const checkPerformance = () => {
            const deviceMemory = navigator.deviceMemory || 8;
            const hardwareConcurrency = navigator.hardwareConcurrency || 4;
            const connection = navigator.connection?.effectiveType || 'unknown';
            
            setDeviceInfo({
                deviceMemory,
                hardwareConcurrency,
                connection
            });

            // 判断是否为低性能设备
            const isLowEnd = (
                deviceMemory < 4 ||
                hardwareConcurrency < 2 ||
                connection === '2g' ||
                connection === 'slow-2g'
            );
            
            setIsLowPerformance(isLowEnd);
        };

        checkPerformance();

        // 监听网络变化
        if (navigator.connection) {
            navigator.connection.addEventListener('change', checkPerformance);
            return () => {
                navigator.connection.removeEventListener('change', checkPerformance);
            };
        }
    }, []);

    return {
        isLowPerformance,
        deviceInfo,
        shouldDisableAnimations: isLowPerformance,
        shouldUseSimplifiedUI: isLowPerformance,
        shouldReduceDataFetching: isLowPerformance || deviceInfo.connection === '2g'
    };
};

// 批量更新优化Hook
export const useBatchUpdates = () => {
    const pendingUpdates = useRef<Map<string, () => void>>(new Map());
    const frameId = useRef<number>();

    const scheduleUpdate = useCallback((key: string, update: () => void) => {
        pendingUpdates.current.set(key, update);

        if (frameId.current) {
            cancelAnimationFrame(frameId.current);
        }

        frameId.current = requestAnimationFrame(() => {
            const updates = Array.from(pendingUpdates.current.values());
            pendingUpdates.current.clear();
            
            // 批量执行更新
            updates.forEach(update => update());
        });
    }, []);

    useEffect(() => {
        return () => {
            if (frameId.current) {
                cancelAnimationFrame(frameId.current);
            }
        };
    }, []);

    return { scheduleUpdate };
};

// Web Worker Hook
export const useWebWorker = <T, R>(workerFunction: (data: T) => R) => {
    const [result, setResult] = useState<R | null>(null);
    const [error, setError] = useState<Error | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const workerRef = useRef<Worker | null>(null);

    useEffect(() => {
        // 创建Web Worker
        const blob = new Blob([
            `self.onmessage = function(e) {
                const fn = ${workerFunction.toString()};
                const result = fn(e.data);
                self.postMessage(result);
            }`
        ], { type: 'application/javascript' });

        const workerUrl = URL.createObjectURL(blob);
        workerRef.current = new Worker(workerUrl);

        workerRef.current.onmessage = (e) => {
            setResult(e.data);
            setIsProcessing(false);
        };

        workerRef.current.onerror = (e) => {
            setError(new Error(e.message));
            setIsProcessing(false);
        };

        return () => {
            workerRef.current?.terminate();
            URL.revokeObjectURL(workerUrl);
        };
    }, []);

    const process = useCallback((data: T) => {
        setIsProcessing(true);
        setError(null);
        workerRef.current?.postMessage(data);
    }, []);

    return { result, error, isProcessing, process };
};