import React, { useState, useEffect } from 'react';
import { Task, MockOrderBook, MockTickData, MockBrokerQueue, LogEntry } from '../../types';
import { OrderBook } from './OrderBook';
import { TickFeed } from './TickFeed';
import { BrokerQueue } from './BrokerQueue';
import { LogSystem } from './LogSystem';

interface TaskDetailsModalProps {
    task: Task;
    onClose: () => void;
}

type LogTabType = 'strategy' | 'trading' | 'system' | 'all';

export const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
    task,
    onClose
}) => {
    const [activeLogTab, setActiveLogTab] = useState<LogTabType>('all');
    
    // 翻译映射
    const strategyTypeMap: Record<string, string> = {
        'strategy_a_big_order_monitor': '策略A: 大单监控',
        'strategy_b_breakout_chase': '策略B: 突破追涨', 
        'strategy_c_reversal': '策略C: 反转交易',
        'strategy_d_momentum': '策略D: 动量交易'
    };
    
    const paramNameMap: Record<string, string> = {
        'monitorThreshold': '监控阈值',
        'durationSeconds': '持续时间(秒)',
        'targetBrokers': '目标经纪商',
        'orderSize': '订单大小',
        'breakoutPeriod': '突破周期',
        'volumeMultiplier': '成交量倍数',
        'pullbackPercent': '回调百分比',
        'oversoldLevel': '超卖水平',
        'rebounds': '反弹次数',
        'volumeFilter': '成交量过滤',
        'lossRatio': '止损比例',
        'stopPrice': '止损价格',
        'basePrice': '基准价格',
        'priceOffset': '价格偏移',
        'direction': '方向',
        'timeoutSeconds': '超时时间(秒)',
        'timeoutAction': '超时动作'
    };
    
    const liquidationTypeMap: Record<string, string> = {
        'market': '市价单',
        'limit_optimized': '限价优化',
        'stop_loss': '止损单',
        'trailing_stop': '追踪止损'
    };
    
    const paramValueMap: Record<string, string> = {
        'bid1': '买一价',
        'ask1': '卖一价',
        'last': '最新价',
        'up': '向上',
        'down': '向下',
        'cancel_and_market': '撤单转市价',
        'cancel_only': '仅撤单',
        'true': '是',
        'false': '否'
    };
    
    // 翻译函数
    const translateStrategyType = (type: string) => {
        return strategyTypeMap[type] || type;
    };
    
    const translateParamName = (name: string) => {
        return paramNameMap[name] || name;
    };
    
    const translateParamValue = (value: any) => {
        if (typeof value === 'string') {
            return paramValueMap[value] || value;
        }
        if (typeof value === 'boolean') {
            return value ? '是' : '否';
        }
        if (Array.isArray(value)) {
            return value.map(v => translateParamValue(v)).join(', ');
        }
        return String(value);
    };
    
    const translateLiquidationType = (type: string) => {
        return liquidationTypeMap[type] || type;
    };
    
    // 模拟实时数据
    const [mockOrderBook, setMockOrderBook] = useState<MockOrderBook>({
        stockCode: task.stockCode,
        bids: Array.from({ length: 10 }, (_, i) => ({
            price: 320.5 - i * 0.1,
            volume: Math.floor(Math.random() * 50000) + 10000,
            brokerIds: ['GS', 'MS', 'UBS'].slice(0, Math.floor(Math.random() * 3) + 1)
        })),
        asks: Array.from({ length: 10 }, (_, i) => ({
            price: 320.6 + i * 0.1,
            volume: Math.floor(Math.random() * 50000) + 10000,
            brokerIds: ['HSBC', 'CITIC', 'HT'].slice(0, Math.floor(Math.random() * 3) + 1)
        })),
        timestamp: new Date()
    });

    const [mockTickData, setMockTickData] = useState<MockTickData[]>([]);
    
    const [mockBrokerQueue, setMockBrokerQueue] = useState<MockBrokerQueue[]>([
        {
            stockCode: task.stockCode,
            side: 'buy',
            priceLevel: 320.5,
            brokers: [
                { brokerId: 'GS001', brokerName: '高盛', volume: 15000, orders: 3 },
                { brokerId: 'MS002', brokerName: '摩根士丹利', volume: 22000, orders: 5 },
                { brokerId: 'UBS003', brokerName: '瑞银', volume: 8000, orders: 2 }
            ],
            timestamp: new Date()
        },
        {
            stockCode: task.stockCode,
            side: 'sell',
            priceLevel: 320.6,
            brokers: [
                { brokerId: 'HSBC001', brokerName: '汇丰', volume: 18000, orders: 4 },
                { brokerId: 'CITIC002', brokerName: '中信', volume: 12000, orders: 3 }
            ],
            timestamp: new Date()
        }
    ]);

    const [mockLogs, setMockLogs] = useState<LogEntry[]>([
        {
            id: 'log_1',
            timestamp: new Date(Date.now() - 5000),
            level: 'info',
            category: 'strategy',
            taskId: task.id,
            message: '策略监控启动，开始监控大单信号',
            details: { threshold: 10000 }
        },
        {
            id: 'log_2',
            timestamp: new Date(Date.now() - 10000),
            level: 'info',
            category: 'trading',
            taskId: task.id,
            message: '连接交易服务器成功',
            details: { server: 'trading.example.com' }
        },
        {
            id: 'log_3',
            timestamp: new Date(Date.now() - 15000),
            level: 'warning',
            category: 'system',
            taskId: task.id,
            message: '检测到网络延迟较高',
            details: { latency: 150 }
        },
        {
            id: 'log_4',
            timestamp: new Date(Date.now() - 20000),
            level: 'info',
            category: 'strategy',
            taskId: task.id,
            message: '发现符合条件的大单信号',
            details: { volume: 25000, broker: 'GS001' }
        }
    ]);

    // 模拟实时数据更新
    useEffect(() => {
        const interval = setInterval(() => {
            // 更新tick数据
            const newTick: MockTickData = {
                stockCode: task.stockCode,
                price: 320.5 + (Math.random() - 0.5) * 0.2,
                volume: Math.floor(Math.random() * 10000) + 1000,
                direction: Math.random() > 0.5 ? 'buy' : 'sell',
                timestamp: new Date(),
                brokerId: ['GS001', 'MS002', 'UBS003', 'HSBC001'][Math.floor(Math.random() * 4)]
            };
            
            setMockTickData(prev => [newTick, ...prev.slice(0, 49)]);

            // 偶尔添加新日志
            if (Math.random() > 0.8) {
                const newLog: LogEntry = {
                    id: `log_${Date.now()}`,
                    timestamp: new Date(),
                    level: Math.random() > 0.7 ? 'warning' : 'info',
                    category: ['strategy', 'trading', 'system'][Math.floor(Math.random() * 3)] as any,
                    taskId: task.id,
                    message: [
                        '策略信号检测中...',
                        '市场数据更新',
                        '风控检查通过',
                        '网络连接正常'
                    ][Math.floor(Math.random() * 4)]
                };
                setMockLogs(prev => [newLog, ...prev.slice(0, 49)]);
            }
        }, 3000);

        return () => clearInterval(interval);
    }, [task.stockCode, task.id]);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    const formatDateTime = (date: Date) => {
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).format(date);
    };

    const getFilteredLogs = () => {
        if (activeLogTab === 'all') return mockLogs;
        return mockLogs.filter(log => log.category === activeLogTab);
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content modal-large task-details-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>详细信息 - {task.stockName} {task.stockCode}</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>
                
                <div className="modal-body">
                    <div className="details-layout">
                        {/* 左侧：基本信息和配置 */}
                        <div className="details-sidebar">
                            {/* 基本信息 */}
                            <div className="details-section">
                                <h3>基本信息</h3>
                                <div className="info-grid">
                                    <div className="info-item">
                                        <label>任务名称:</label>
                                        <span>{task.name}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>股票代码:</label>
                                        <span>{task.stockCode}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>股票名称:</label>
                                        <span>{task.stockName}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>策略类型:</label>
                                        <span>{task.strategyName}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>当前状态:</label>
                                        <span className={`status-badge status-${task.status}`}>
                                            {task.status === 'running' ? '运行中' : 
                                             task.status === 'paused' ? '已暂停' : 
                                             task.status === 'error' ? '错误' : '已停止'}
                                        </span>
                                    </div>
                                    <div className="info-item">
                                        <label>创建时间:</label>
                                        <span>{formatDateTime(task.createdAt)}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>更新时间:</label>
                                        <span>{formatDateTime(task.updatedAt)}</span>
                                    </div>
                                </div>
                            </div>

                            {/* 持仓信息 */}
                            <div className="details-section">
                                <h3>持仓信息</h3>
                                <div className="info-grid">
                                    <div className="info-item">
                                        <label>持仓数量:</label>
                                        <span>{task.position.toLocaleString()} 股</span>
                                    </div>
                                    {task.avgCost && (
                                        <div className="info-item">
                                            <label>平均成本:</label>
                                            <span>{formatCurrency(task.avgCost)}</span>
                                        </div>
                                    )}
                                    <div className="info-item">
                                        <label>浮动盈亏:</label>
                                        <span className={task.pnl >= 0 ? 'profit' : 'loss'}>
                                            {formatCurrency(task.pnl)}
                                        </span>
                                    </div>
                                    {task.position > 0 && task.avgCost && (
                                        <div className="info-item">
                                            <label>持仓市值:</label>
                                            <span>{formatCurrency(task.position * task.avgCost + task.pnl)}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 策略配置 */}
                            <div className="details-section">
                                <h3>策略配置</h3>
                                <div className="strategy-config">
                                    <div className="config-item">
                                        <label>策略类型:</label>
                                        <span>{task.strategyConfig.strategyType}</span>
                                    </div>
                                    <div className="config-params">
                                        <label>策略参数:</label>
                                        <div className="params-list">
                                            {Object.entries(task.strategyConfig.params || {}).map(([key, value]) => (
                                                <div key={key} className="param-item">
                                                    <span className="param-key">{key}:</span>
                                                    <span className="param-value">
                                                        {Array.isArray(value) ? value.join(', ') : String(value)}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 风控配置 */}
                            <div className="details-section">
                                <h3>风控配置</h3>
                                <div className="risk-config">
                                    <div className="config-item">
                                        <label>触发逻辑:</label>
                                        <span>{task.riskConfig.triggerLogic === 'any' ? '满足任一条件' : '满足全部条件'}</span>
                                    </div>
                                    <div className="config-item">
                                        <label>风险条件数:</label>
                                        <span>{task.riskConfig.conditions.length}</span>
                                    </div>
                                    <div className="config-item">
                                        <label>清仓策略:</label>
                                        <span>{task.riskConfig.liquidationStrategy.type}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 右侧：实时数据区域 */}
                        <div className="details-main">
                            <div className="realtime-data-tabs">
                                <div className="data-tab-group">
                                    <div className="data-tab active">
                                        <OrderBook orderBook={mockOrderBook} />
                                    </div>
                                </div>
                                
                                <div className="data-tab-group">
                                    <div className="data-tab">
                                        <TickFeed tickData={mockTickData} maxItems={15} />
                                    </div>
                                </div>
                                
                                <div className="data-tab-group">
                                    <div className="data-tab">
                                        <BrokerQueue brokerQueue={mockBrokerQueue} />
                                    </div>
                                </div>
                            </div>

                            {/* 日志区域 */}
                            <div className="details-section logs-section">
                                <div className="log-tabs-header">
                                    <h3>任务日志</h3>
                                    <div className="log-tabs">
                                        <button 
                                            className={`tab-button ${activeLogTab === 'all' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('all')}
                                        >
                                            全部
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'strategy' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('strategy')}
                                        >
                                            策略日志
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'trading' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('trading')}
                                        >
                                            交易日志
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'system' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('system')}
                                        >
                                            系统日志
                                        </button>
                                    </div>
                                </div>
                                <div className="log-content">
                                    <LogSystem 
                                        logs={getFilteredLogs()}
                                        maxItems={20}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div className="modal-footer">
                    <button className="btn btn-primary" onClick={onClose}>
                        关闭
                    </button>
                </div>
            </div>
        </div>
    );
};